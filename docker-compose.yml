services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ecotask-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ecotask
      POSTGRES_USER: ecotask_user
      POSTGRES_PASSWORD: ecotask_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - ecotask-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: ecotask-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************************/ecotask
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - ecotask-network
    command: npm run dev

  # Frontend React/Vite
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ecotask-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:3001
    ports:
      - "5173:5173"
    depends_on:
      - backend
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - ecotask-network

volumes:
  postgres_data:

networks:
  ecotask-network:
    driver: bridge
