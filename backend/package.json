{"name": "ecotask-backend", "version": "1.0.0", "description": "Backend API pour EcoTask", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx src/utils/seed.ts", "db:studio": "prisma studio", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "zod": "^3.22.4", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "prisma": "^5.7.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "jest": "^29.7.0", "@types/jest": "^29.5.11"}, "keywords": ["ecotask", "api", "postgresql", "express", "typescript"], "author": "EcoTask Team", "license": "MIT"}