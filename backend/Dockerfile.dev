FROM node:18-alpine

# Installer les dépendances système nécessaires
RUN apk add --no-cache openssl

# C<PERSON>er le répertoire de l'application
WORKDIR /app

# Copier les fichiers de configuration
COPY package*.json ./
COPY tsconfig.json ./
COPY prisma ./prisma/

# Installer toutes les dépendances (y compris dev)
RUN npm ci

# Générer le client Prisma
RUN npx prisma generate

# Exposer le port
EXPOSE 3001

# Variables d'environnement par défaut
ENV NODE_ENV=development
ENV PORT=3001

# Commande de démarrage en mode développement
CMD ["npm", "run", "dev"]
