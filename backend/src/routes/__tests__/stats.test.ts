import request from 'supertest'
import express from 'express'
import { TaskTypeEnum, Priority, Status } from '@prisma/client'
import statsRouter from '../stats'
import { mockPrisma } from '../../test/setup'

// Mock des middleware de validation
jest.mock('../../utils/validators', () => ({
  validate: () => (req: any, res: any, next: any) => {
    req.validatedData = req.body || req.query || req.params
    next()
  }
}))

const app = express()
app.use(express.json())
app.use('/api/stats', statsRouter)

describe('Stats Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/stats/overview', () => {
    it('should return global statistics', async () => {
      // Mock des données pour les statistiques globales
      mockPrisma.user.count.mockResolvedValue(10)
      mockPrisma.project.count.mockResolvedValue(5)
      mockPrisma.task.count
        .mockResolvedValueOnce(25) // total tasks
        .mockResolvedValueOnce(15) // completed tasks
      
      // Mock pour les tâches avec CO2
      mockPrisma.task.findMany.mockResolvedValue([
        { co2Emissions: 5.0 },
        { co2Emissions: 7.5 },
        { co2Emissions: 2.5 },
      ])

      // Mock pour les tâches par statut
      mockPrisma.task.groupBy.mockResolvedValue([
        { status: Status.TODO, _count: { status: 5 } },
        { status: Status.IN_PROGRESS, _count: { status: 5 } },
        { status: Status.DONE, _count: { status: 15 } },
      ])

      const response = await request(app)
        .get('/api/stats/overview')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.totalUsers).toBe(10)
      expect(response.body.data.totalProjects).toBe(5)
      expect(response.body.data.totalTasks).toBe(25)
      expect(response.body.data.completedTasks).toBe(15)
      expect(response.body.data.completionRate).toBe(60)
      expect(response.body.data.totalCO2).toBe(15.0)
      expect(response.body.data.tasksByStatus).toHaveLength(3)
    })

    it('should handle empty database', async () => {
      // Mock pour une base de données vide
      mockPrisma.user.count.mockResolvedValue(0)
      mockPrisma.project.count.mockResolvedValue(0)
      mockPrisma.task.count.mockResolvedValue(0)
      mockPrisma.task.findMany.mockResolvedValue([])
      mockPrisma.task.groupBy.mockResolvedValue([])

      const response = await request(app)
        .get('/api/stats/overview')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.totalUsers).toBe(0)
      expect(response.body.data.totalProjects).toBe(0)
      expect(response.body.data.totalTasks).toBe(0)
      expect(response.body.data.completedTasks).toBe(0)
      expect(response.body.data.completionRate).toBe(0)
      expect(response.body.data.totalCO2).toBe(0)
      expect(response.body.data.tasksByStatus).toHaveLength(0)
    })
  })

  describe('GET /api/stats/co2', () => {
    it('should return CO2 statistics', async () => {
      // Mock des tâches par type
      mockPrisma.task.groupBy.mockResolvedValue([
        { type: TaskTypeEnum.LIGHT, _sum: { co2Emissions: 5.0 }, _count: { type: 10 } },
        { type: TaskTypeEnum.TECHNICAL, _sum: { co2Emissions: 25.0 }, _count: { type: 15 } },
        { type: TaskTypeEnum.INTENSIVE, _sum: { co2Emissions: 35.0 }, _count: { type: 8 } },
      ])

      // Mock des projets avec le plus de CO2
      mockPrisma.project.findMany.mockResolvedValue([
        {
          id: 'clp123456789abcdef01',
          name: 'Project Alpha',
          totalCO2: 25.0,
          tasks: [{ co2Emissions: 25.0 }]
        },
        {
          id: 'clp123456789abcdef02',
          name: 'Project Beta',
          totalCO2: 20.0,
          tasks: [{ co2Emissions: 20.0 }]
        },
      ])

      const response = await request(app)
        .get('/api/stats/co2')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.totalCO2).toBe(65.0)
      expect(response.body.data.averageCO2PerTask).toBe(1.97)
      expect(response.body.data.co2ByType).toHaveLength(3)
      expect(response.body.data.topCO2Projects).toHaveLength(2)
    })

    it('should handle no CO2 data', async () => {
      mockPrisma.task.groupBy.mockResolvedValue([])
      mockPrisma.project.findMany.mockResolvedValue([])

      const response = await request(app)
        .get('/api/stats/co2')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.totalCO2).toBe(0)
      expect(response.body.data.averageCO2PerTask).toBe(0)
      expect(response.body.data.co2ByType).toHaveLength(0)
      expect(response.body.data.topCO2Projects).toHaveLength(0)
    })
  })

  describe('GET /api/stats/productivity', () => {
    it('should return productivity statistics', async () => {
      // Mock des tâches par priorité
      mockPrisma.task.groupBy.mockResolvedValue([
        { priority: Priority.LOW, _count: { priority: 5 } },
        { priority: Priority.MEDIUM, _count: { priority: 15 } },
        { priority: Priority.HIGH, _count: { priority: 10 } },
      ])

      // Mock des utilisateurs les plus productifs
      mockPrisma.user.findMany.mockResolvedValue([
        {
          id: 'clp123456789abcdef01',
          name: 'John Doe',
          email: '<EMAIL>',
          tasks: [
            { status: Status.DONE },
            { status: Status.DONE },
            { status: Status.IN_PROGRESS },
          ]
        },
        {
          id: 'clp123456789abcdef02',
          name: 'Jane Smith',
          email: '<EMAIL>',
          tasks: [
            { status: Status.DONE },
            { status: Status.TODO },
          ]
        },
      ])

      // Mock des tâches récemment terminées
      mockPrisma.task.findMany.mockResolvedValue([
        {
          id: 'clp123456789abcdef03',
          title: 'Completed Task 1',
          status: Status.DONE,
          completedAt: new Date(),
          assignee: { name: 'John Doe' },
          project: { name: 'Project Alpha' }
        },
        {
          id: 'clp123456789abcdef04',
          title: 'Completed Task 2',
          status: Status.DONE,
          completedAt: new Date(),
          assignee: { name: 'Jane Smith' },
          project: { name: 'Project Beta' }
        },
      ])

      const response = await request(app)
        .get('/api/stats/productivity')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.tasksByPriority).toHaveLength(3)
      expect(response.body.data.topPerformers).toHaveLength(2)
      expect(response.body.data.recentCompletions).toHaveLength(2)
    })
  })

  describe('GET /api/stats/trends', () => {
    it('should return trend statistics', async () => {
      // Mock des tâches créées par jour (derniers 30 jours)
      const mockTasksCreated = Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        count: Math.floor(Math.random() * 10) + 1
      }))

      // Mock des tâches terminées par jour
      const mockTasksCompleted = Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        count: Math.floor(Math.random() * 8) + 1
      }))

      // Mock des émissions CO2 par jour
      const mockCO2Trends = Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        co2: Math.random() * 50 + 10
      }))

      // Mock des requêtes Prisma pour les tendances
      mockPrisma.$queryRaw
        .mockResolvedValueOnce(mockTasksCreated)
        .mockResolvedValueOnce(mockTasksCompleted)
        .mockResolvedValueOnce(mockCO2Trends)

      const response = await request(app)
        .get('/api/stats/trends')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.tasksCreated).toHaveLength(30)
      expect(response.body.data.tasksCompleted).toHaveLength(30)
      expect(response.body.data.co2Trends).toHaveLength(30)
    })
  })
})
