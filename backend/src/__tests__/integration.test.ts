import { describe, it, expect, beforeEach } from '@jest/globals'
import request from 'supertest'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import { errorHandler } from '../middleware/errorHandler'
import { notFound } from '../middleware/notFound'
import usersRouter from '../routes/users'
import projectsRouter from '../routes/projects'
import tasksRouter from '../routes/tasks'
import statsRouter from '../routes/stats'
import { mockPrisma } from '../test/setup'

// Créer l'application Express pour les tests
const createApp = () => {
  const app = express()

  // Middleware
  app.use(helmet())
  app.use(cors())
  app.use(compression())
  app.use(express.json())

  // Health check
  app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() })
  })

  // Routes API
  app.use('/api/users', usersRouter)
  app.use('/api/projects', projectsRouter)
  app.use('/api/tasks', tasksRouter)
  app.use('/api/stats', statsRouter)

  // Middleware d'erreur
  app.use(notFound)
  app.use(errorHandler)

  return app
}

describe('API Integration Tests', () => {
  let app: express.Application

  beforeEach(() => {
    jest.clearAllMocks()
    app = createApp()
  })

  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.body.status).toBe('OK')
      expect(response.body.timestamp).toBeDefined()
    })
  })

  describe('Error Handling', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/api/unknown')
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toBe('Route non trouvée')
    })
  })

  describe('CORS and Security Headers', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.headers['access-control-allow-origin']).toBeDefined()
    })

    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.headers['x-content-type-options']).toBe('nosniff')
      expect(response.headers['x-frame-options']).toBeDefined()
    })
  })

  describe('API Workflow', () => {
    it('should handle basic API calls', async () => {
      // Test simple de santé de l'API
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.body.status).toBe('OK')
      expect(response.body.timestamp).toBeDefined()
    })
  })

  describe('Data Validation', () => {
    it('should handle basic data validation', async () => {
      // Test simple de validation
      expect(true).toBe(true)
    })
  })
})
