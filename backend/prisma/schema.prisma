// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  role      Role     @default(MEMBER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ownedProjects      Project[]       @relation("ProjectOwner")
  assignedTasks      Task[]          @relation("TaskAssignee")
  projectMemberships ProjectMember[]

  @@map("users")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  color       String   @default("#10b981")
  totalCO2    Float    @default(0.0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  ownerId String
  owner   User   @relation("ProjectOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  tasks   Task[]
  members ProjectMember[]

  @@map("projects")
}

model ProjectMember {
  projectId String
  userId    String
  joinedAt  DateTime @default(now())

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([projectId, userId])
  @@map("project_members")
}

model Task {
  id             String    @id @default(cuid())
  title          String
  description    String?
  type           TaskTypeEnum
  priority       Priority
  status         Status    @default(TODO)
  estimatedHours Float     @default(1.0)
  actualHours    Float     @default(0.0)
  co2Emissions   Float     @default(0.0)
  dueDate        DateTime
  completedAt    DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  assigneeId String
  assignee   User   @relation("TaskAssignee", fields: [assigneeId], references: [id], onDelete: Cascade)

  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("tasks")
}



// Enums
enum Role {
  ADMIN
  MEMBER
}

enum TaskTypeEnum {
  LIGHT
  TECHNICAL
  INTENSIVE
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum Status {
  TODO
  IN_PROGRESS
  REVIEW
  DONE
}
