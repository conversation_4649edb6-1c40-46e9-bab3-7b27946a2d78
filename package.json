{"name": "ecotaskv2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "setup": "./scripts/setup.sh", "dev:full": "./scripts/dev.sh", "dev:docker": "./scripts/docker-dev.sh", "dev:docker:simple": "./scripts/docker-simple.sh", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "docker:build": "docker compose build", "docker:test": "./scripts/test-docker.sh", "db:studio": "cd backend && npm run db:studio"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}